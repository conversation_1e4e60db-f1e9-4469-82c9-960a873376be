#!/bin/bash

# Deploy script for Fly.io with GitHub
# This script sets secrets and deploys the app

APP_NAME="realfluence-ancient-voice-509"

echo "🚀 Deploying $APP_NAME to Fly.io..."

# Check if required secrets are set in GitHub
if [ -z "$SHOPIFY_API_SECRET" ]; then
    echo "❌ SHOPIFY_API_SECRET is not set"
    exit 1
fi

echo "📝 Setting secrets..."

# Set secrets from GitHub environment
flyctl secrets set \
    SHOPIFY_API_SECRET="$SHOPIFY_API_SECRET" \
    AWS_ACCESS_KEY_ID="$AWS_ACCESS_KEY_ID" \
    AWS_SECRET_ACCESS_KEY="$AWS_SECRET_ACCESS_KEY" \
    S3_BUCKET="testtrustpeer" \
    S3_REGION="us-east-1" \
    -a $APP_NAME

echo "🚢 Deploying application..."
flyctl deploy --remote-only -a $APP_NAME

echo "✅ Deployment complete!"
echo "🌐 App URL: https://$APP_NAME.fly.dev"
