name: Deploy to Fly.io

on:
  push:
    branches: [main]
  workflow_dispatch:

jobs:
  deploy:
    name: Deploy app
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - uses: superfly/flyctl-actions/setup-flyctl@master

      - name: Set secrets
        run: |
          echo "Setting environment variables..."
          flyctl secrets set SHOPIFY_API_SECRET="${{ secrets.SHOPIFY_API_SECRET }}" -a realfluence-ancient-voice-509
          flyctl secrets set AWS_ACCESS_KEY_ID="${{ secrets.AWS_ACCESS_KEY_ID }}" -a realfluence-ancient-voice-509
          flyctl secrets set AWS_SECRET_ACCESS_KEY="${{ secrets.AWS_SECRET_ACCESS_KEY }}" -a realfluence-ancient-voice-509
          flyctl secrets set S3_BUCKET="testtrustpeer" -a realfluence-ancient-voice-509
          flyctl secrets set S3_REGION="us-east-1" -a realfluence-ancient-voice-509
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

      - name: Deploy to Fly.io
        run: flyctl deploy --remote-only -a realfluence-ancient-voice-509
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}
