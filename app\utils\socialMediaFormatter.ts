import { getSignedVideoUrl } from './storage';

export interface PlatformSpecs {
  aspectRatio: string;
  maxDuration: number; // in seconds
  maxFileSize: number; // in MB
  recommendedResolution: {
    width: number;
    height: number;
  };
  format: string;
}

export const PLATFORM_SPECS: Record<string, PlatformSpecs> = {
  instagram: {
    aspectRatio: '9:16',
    maxDuration: 90,
    maxFileSize: 100,
    recommendedResolution: {
      width: 1080,
      height: 1920
    },
    format: 'mp4'
  },
  tiktok: {
    aspectRatio: '9:16',
    maxDuration: 180,
    maxFileSize: 287,
    recommendedResolution: {
      width: 1080,
      height: 1920
    },
    format: 'mp4'
  },
  twitter: {
    aspectRatio: '16:9',
    maxDuration: 140,
    maxFileSize: 512,
    recommendedResolution: {
      width: 1920,
      height: 1080
    },
    format: 'mp4'
  }
};

export interface VideoFormatOptions {
  platform: keyof typeof PLATFORM_SPECS;
  addBranding?: boolean;
  addCaptions?: boolean;
}

export interface FormattedVideoResult {
  url: string;
  platform: string;
  specs: PlatformSpecs;
  downloadUrl?: string;
}

/**
 * Generate a formatted video for a specific social media platform
 * This is a simplified version - in production you'd use FFmpeg or similar
 */
export async function formatVideoForPlatform(
  originalVideoUrl: string,
  options: VideoFormatOptions
): Promise<FormattedVideoResult> {
  const specs = PLATFORM_SPECS[options.platform];
  
  if (!specs) {
    throw new Error(`Unsupported platform: ${options.platform}`);
  }

  try {
    // For now, we'll return the original video URL with platform specs
    // In a full implementation, you would:
    // 1. Download the original video
    // 2. Use FFmpeg to resize/crop to the correct aspect ratio
    // 3. Add platform-specific optimizations
    // 4. Upload the processed video
    // 5. Return the new URL

    // Get signed URL for the original video
    const signedUrl = await getSignedVideoUrl(originalVideoUrl);
    
    return {
      url: signedUrl,
      platform: options.platform,
      specs,
      downloadUrl: signedUrl
    };
  } catch (error) {
    console.error(`Error formatting video for ${options.platform}:`, error);
    throw new Error(`Failed to format video for ${options.platform}`);
  }
}

/**
 * Generate share URLs for different platforms
 */
export function generateShareUrls(videoUrl: string, caption: string, productName: string) {
  const encodedCaption = encodeURIComponent(caption);
  const encodedUrl = encodeURIComponent(videoUrl);
  
  return {
    instagram: {
      url: 'https://www.instagram.com/',
      note: 'Download the video and share manually on Instagram',
      action: 'download'
    },
    tiktok: {
      url: 'https://www.tiktok.com/upload',
      note: 'Download the video and upload to TikTok',
      action: 'download'
    },
    twitter: {
      url: `https://twitter.com/intent/tweet?text=${encodedCaption}&url=${encodedUrl}`,
      note: 'Share directly on X (Twitter)',
      action: 'share'
    },
    facebook: {
      url: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}&quote=${encodedCaption}`,
      note: 'Share on Facebook',
      action: 'share'
    },
    whatsapp: {
      url: `https://wa.me/?text=${encodedCaption}%20${encodedUrl}`,
      note: 'Share on WhatsApp',
      action: 'share'
    }
  };
}

/**
 * Generate platform-specific captions
 */
export function generatePlatformCaption(
  platform: keyof typeof PLATFORM_SPECS,
  productName: string,
  customerName: string,
  storeName?: string
): string {
  const baseMessage = `Amazing testimonial for ${productName}! 🌟`;
  
  switch (platform) {
    case 'instagram':
      return `${baseMessage}\n\n✨ Real customer review from ${customerName}\n${storeName ? `🛍️ Shop at ${storeName}` : ''}\n\n#testimonial #review #${productName.replace(/\s+/g, '').toLowerCase()} #customerreview`;
    
    case 'tiktok':
      return `${baseMessage} Real review from ${customerName}! ${storeName ? `Shop at ${storeName}` : ''} #testimonial #review #${productName.replace(/\s+/g, '').toLowerCase()}`;
    
    case 'twitter':
      return `${baseMessage} Real customer review from ${customerName}. ${storeName ? `Shop at ${storeName}` : ''} #testimonial #review`;
    
    default:
      return `${baseMessage} Real customer review from ${customerName}.`;
  }
}
