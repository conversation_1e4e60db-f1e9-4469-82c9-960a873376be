class ProductTestimonials {
  constructor() {
    this.container = document.querySelector('.testimonials-container');
    if (!this.container) return;
    
    this.productId = this.container.dataset.productId;
    this.storeDomain = this.container.dataset.storeDomain;
    this.layout = this.container.dataset.layout;
    this.perPage = parseInt(this.container.dataset.perPage) || 3;
    this.proxyUrl = this.container.dataset.proxyUrl;
    
    this.loadingSpinner = this.container.querySelector('.testimonials-loading-spinner');
    this.noTestimonialsMessage = this.container.querySelector('.no-testimonials-message');
    this.testimonialsGrid = this.container.querySelector('.testimonials-grid');
    this.testimonialsCarousel = this.container.querySelector('.testimonials-carousel');
    this.carouselContainer = this.container.querySelector('.carousel-container');
    this.testimonialsPaginated = this.container.querySelector('.testimonials-paginated');
    this.testimonialsBar = this.container.querySelector('.testimonials-bar');
    
    // Pagination elements
    this.paginationPrev = this.container.querySelector('.pagination-button.prev');
    this.paginationNext = this.container.querySelector('.pagination-button.next');
    this.currentPageElement = this.container.querySelector('.current-page');
    this.totalPagesElement = this.container.querySelector('.total-pages');
    
    this.currentPage = 1;
    this.testimonials = [];
    this.totalPages = 1;
    
    this.init();
  }
  
  init() {
    this.fetchTestimonials();
    this.initCarouselControls();
    this.initPaginationControls();
  }
  
  async fetchTestimonials() {
    try {
      this.showLoading(true);
      
      // Generate timestamp for HMAC validation
      const timestamp = Math.floor(Date.now() / 1000).toString();
      const proxy = this.proxyUrl || "https://realfluence.ai/app/proxy";
      
      // Fetch testimonials from the app proxy
      const response = await fetch(`${proxy}?action=fetchProductTestimonials&timestamp=${timestamp}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: 'include',
        body: JSON.stringify({
          productId: this.productId,
          store: this.storeDomain
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch testimonials');
      }
      
      console.log("response", response)
      this.testimonials = await response.json();
      console.log("testimonials", this.testimonials)
      
      if (this.testimonials && this.testimonials.length > 0 && !this.testimonials.error) {
        this.totalPages = Math.ceil(this.testimonials.length / this.perPage);
        this.totalPagesElement.textContent = this.totalPages;
        this.renderTestimonials();
      } else {
        this.noTestimonialsMessage.style.display = 'block';
      }
    } catch (error) {
      console.error('Error fetching testimonials:', error);
      this.showError();
    } finally {
      this.showLoading(false);
    }
  }
  
  renderTestimonials() {
    if (!this.testimonials || this.testimonials.length === 0 || this.testimonials.error) {
      this.noTestimonialsMessage.style.display = 'block';
      return;
    }
    
    // Display based on layout setting
    if (this.layout === 'carousel') {
      // Create all testimonial cards for carousel
      const testimonialElements = this.testimonials.map(testimonial => 
        this.createTestimonialCard(testimonial)
      );
      testimonialElements.forEach(el => this.carouselContainer.appendChild(el));
      this.testimonialsCarousel.style.display = 'flex';
    } 
    else if (this.layout === 'grid') {
      // Create all testimonial cards for grid
      const testimonialElements = this.testimonials.map(testimonial => 
        this.createTestimonialCard(testimonial)
      );
      testimonialElements.forEach(el => this.testimonialsGrid.appendChild(el));
      this.testimonialsGrid.style.display = 'grid';
    }
    else if (this.layout === 'paginated') {
      this.renderPaginatedTestimonials();
      this.testimonialsPaginated.style.display = 'flex';
    }
  }
  
  renderPaginatedTestimonials() {
    // Clear current testimonials
    this.testimonialsBar.innerHTML = '';
    
    // Calculate start and end indices for the current page
    const startIndex = (this.currentPage - 1) * this.perPage;
    const endIndex = Math.min(startIndex + this.perPage, this.testimonials.length);
    
    // Get current page testimonials
    const currentPageTestimonials = this.testimonials.slice(startIndex, endIndex);
    
    // Create and append testimonial cards
    currentPageTestimonials.forEach(testimonial => {
      const card = this.createTestimonialCard(testimonial);
      this.testimonialsBar.appendChild(card);
    });
    
    // Update pagination buttons state
    this.updatePaginationControls();
  }
  
  createTestimonialCard(testimonial) {
    const card = document.createElement('div');
    card.className = 'testimonial-card';
  
    const videoContainer = document.createElement('div');
    videoContainer.className = 'testimonial-video-container';
  
    // Placeholder image or just a play button overlay
    const placeholder = document.createElement('div');
    placeholder.className = 'testimonial-placeholder';
    placeholder.innerHTML = `
      <div class="play-button"></div>
      <p>Click to play testimonial</p>
    `;
  
    // Click to replace with actual video element
    placeholder.addEventListener('click', () => {
      const video = document.createElement('video');
      video.className = 'testimonial-video';
      video.src = testimonial.videoUrl; // Local for now, CDN later
      video.controls = true;
      video.autoplay = true;
      video.preload = 'auto';
      video.style.width = '100%';
  
      videoContainer.innerHTML = ''; // Clear placeholder
      videoContainer.appendChild(video);
    });
  
    videoContainer.appendChild(placeholder);
  
    const meta = document.createElement('div');
    meta.className = 'testimonial-meta';
  
    const name = document.createElement('div');
    name.className = 'testimonial-name';
    name.textContent = testimonial.customerName || 'Customer';
  
    const date = document.createElement('div');
    date.className = 'testimonial-date';
    date.textContent = new Date(testimonial.createdAt).toLocaleDateString();
  
    meta.appendChild(name);
    meta.appendChild(date);
  
    card.appendChild(videoContainer);
    card.appendChild(meta);
  
    return card;
  }
  
  initCarouselControls() {
    const prevButton = this.container.querySelector('.carousel-control.prev');
    const nextButton = this.container.querySelector('.carousel-control.next');
    
    if (prevButton && nextButton && this.carouselContainer) {
      prevButton.addEventListener('click', () => {
        this.carouselContainer.scrollBy({
          left: -320,
          behavior: 'smooth'
        });
      });
      
      nextButton.addEventListener('click', () => {
        this.carouselContainer.scrollBy({
          left: 320,
          behavior: 'smooth'
        });
      });
    }
  }
  
  initPaginationControls() {
    if (this.paginationPrev && this.paginationNext) {
      this.paginationPrev.addEventListener('click', () => {
        if (this.currentPage > 1) {
          this.currentPage--;
          this.currentPageElement.textContent = this.currentPage;
          this.renderPaginatedTestimonials();
        }
      });
      
      this.paginationNext.addEventListener('click', () => {
        if (this.currentPage < this.totalPages) {
          this.currentPage++;
          this.currentPageElement.textContent = this.currentPage;
          this.renderPaginatedTestimonials();
        }
      });
    }
  }
  
  updatePaginationControls() {
    // Disable/enable prev button
    this.paginationPrev.disabled = this.currentPage === 1;
    
    // Disable/enable next button  
    this.paginationNext.disabled = this.currentPage === this.totalPages;
  }
  
  showLoading(isLoading) {
    this.loadingSpinner.style.display = isLoading ? 'block' : 'none';
  }
  
  showError() {
    this.noTestimonialsMessage.textContent = 'Unable to load testimonials. Please try again later.';
    this.noTestimonialsMessage.style.display = 'block';
  }
}

// Initialize testimonials when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new ProductTestimonials();
});