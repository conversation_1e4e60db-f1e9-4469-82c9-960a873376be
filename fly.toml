# fly.toml app configuration file generated for realfluence-ancient-voice-509 on 2025-06-29T22:18:01+05:30
#
# See https://fly.io/docs/reference/configuration/ for information about how to use this file.
#

app = 'realfluence-ancient-voice-509'
primary_region = 'bom'

[build]

[env]
  PORT = '3000'
  NODE_ENV = 'production'
  SCOPES = 'read_customers,read_discounts,read_orders,write_discounts,write_products'
  SHOPIFY_API_KEY = '5e08ea721cf5671aa6f3efc696843904'
  SHOPIFY_APP_URL = 'https://realfluence-ancient-voice-509.fly.dev'
  S3_BUCKET = 'testtrustpeer'
  S3_REGION = 'us-east-1'

# Secrets (set via GitHub Actions or fly CLI):
# SHOPIFY_API_SECRET - set via GitHub secrets
# AWS_ACCESS_KEY_ID - set via GitHub secrets
# AWS_SECRET_ACCESS_KEY - set via GitHub secrets

[[mounts]]
  source = 'data'
  destination = '/data'
  auto_extend_size_threshold = 80
  auto_extend_size_increment = '1GB'
  auto_extend_size_limit = '10GB'

[http_service]
  internal_port = 3000
  force_https = true
  auto_stop_machines = 'stop'
  auto_start_machines = true
  min_machines_running = 0
  processes = ['app']

[[vm]]
  memory = '1gb'
  cpu_kind = 'shared'
  cpus = 1
